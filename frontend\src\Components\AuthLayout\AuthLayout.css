.auth-container {
  display: flex;
  height: 100vh;
  background-color: #1DB954;
}

/* Left column */
.lottie-section {
  flex: 1;
  display: flex;
  flex-direction: column;  /* Text above Lottie */
  justify-content: center;
  align-items: center;
  padding: 20px;
  box-sizing: border-box;
}

.lottie-text {
  text-align: center;
  color: white;
  margin-bottom: 20px;
}

.lottie-text h1 {
  font-size: 2.5rem;
  margin-bottom: 8px;
  font-weight: bolder;
  letter-spacing: 1px;
}

.lottie-text p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.lottie-section canvas {
  width: 100%;
  max-width: 1000px;
  height: 100%;
}


.form-section {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}
