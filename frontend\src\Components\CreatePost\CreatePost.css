.create-post-container {
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
}

.create-post-header {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f0f2f5;
  padding-bottom: 15px;
  margin-bottom: 15px;
}

.avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-right: 15px;
}

.post-input {
  flex-grow: 1; /* Takes up remaining space */
  padding: 12px 20px;
  border: none;
  border-radius: 50px;
  background-color: #f0f2f5;
  font-size: 1rem;
}

.post-input:focus {
  outline: none;
}

.create-post-actions {
  display: flex;
  justify-content: space-around;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-grow: 1;
  padding: 10px;
  border: none;
  background-color: transparent;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  color: #65676b;
  cursor: pointer;
  transition: background-color 0.2s;
  justify-content: center;
}

.action-button:hover {
  background-color: #f0f2f5;
}