.create-post-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  margin: 20px; /* Adds space around the component */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
}

.create-post-header {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f0f2f5;
  padding-bottom: 15px;
  margin-bottom: 15px;
}

.avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-right: 15px;
}

.post-input {
  flex-grow: 1; /* Takes up remaining space */
  padding: 12px 20px;
  border: none;
  border-radius: 50px;
  background-color: #f0f2f5;
  font-size: 1rem;
}

.post-input:focus {
  outline: none;
}

.create-post-actions {
  display: flex;
  justify-content: space-around;
}

.action-button {
  flex-grow: 1; /* Makes buttons share space equally */
  padding: 10px;
  border: none;
  background-color: transparent;
  border-radius: 8px;
  font-weight: bold;
  color: #65676b;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-button:hover {
  background-color: #f0f2f5;
}