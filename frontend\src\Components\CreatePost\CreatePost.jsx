import React from 'react';
import './CreatePost.css';
import { <PERSON>, <PERSON>, Smile } from 'lucide-react';

const CreatePost = () => {
  return (
    <div className='create-post-container'>
      <div className='create-post-header'>
        <img
          src="https://i.pravatar.cc/50?seed=user"
          alt="User Avatar"
          className='avatar'
        />
        <input
          type="text"
          className='post-input'
          placeholder="What's happening at Vignan University?"
        />
      </div>
      <div className='create-post-actions'>
        <button className='action-button'>
          <Camera size={16} />
          Photo/Video
        </button>
        <button className='action-button'>
          <Users size={16} />
          Tag Friends
        </button>
        <button className='action-button'>
          <Smile size={16} />
          Feeling/Activity
        </button>
      </div>
    </div>
  );
};

export default CreatePost;