import React from 'react';
import './Navbar.css';
import { Search, Bell, MessageCircle, Settings } from 'lucide-react';

const Navbar = () => {
  return (
    <nav className='navbar'>
      <h3>VUnity</h3>
      <div className='search-container'>
        <Search size={18} className='search-icon' />
        <input type="text" placeholder="Search students, posts, events..." className='search-input' />
      </div>
      <div className='navbar-actions'>
        <button className='nav-btn'>
          <MessageCircle size={20} />
        </button>
        <button className='nav-btn'>
          <Bell size={20} />
          <span className='notification-badge'>3</span>
        </button>
        <button className='nav-btn'>
          <Settings size={20} />
        </button>
        <div className='profile-section'>
          <img
            src="https://i.pravatar.cc/32?seed=currentuser"
            alt="Profile"
            className='profile-avatar'
          />
          <span className='profile-name'>You</span>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;