.post {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
  transition: box-shadow 0.2s ease;
}

.post:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.post-user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.post-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.post-user-details {
  display: flex;
  flex-direction: column;
}

.post-user-name {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  line-height: 1.2;
}

.post-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 2px;
}

.post-department {
  font-size: 13px;
  color: #4CAF50;
  font-weight: 500;
}

.post-time {
  font-size: 13px;
  color: #657786;
}

.post-menu {
  background: none;
  border: none;
  color: #657786;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.post-menu:hover {
  background-color: #f7f9fa;
}

.post-content {
  margin-bottom: 15px;
}

.post-content p {
  font-size: 15px;
  line-height: 1.6;
  color: #1a1a1a;
  margin: 0 0 12px 0;
}

.post-image {
  width: 100%;
  border-radius: 12px;
  margin-top: 12px;
  object-fit: cover;
  max-height: 400px;
}

.post-hashtags {
  margin-top: 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.hashtag {
  color: #4CAF50;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.2s;
}

.hashtag:hover {
  color: #2E7D32;
}

.post-stats {
  display: flex;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #e1e8ed;
  margin-bottom: 12px;
  font-size: 13px;
  color: #657786;
}

.post-actions {
  display: flex;
  justify-content: space-around;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  color: #657786;
  cursor: pointer;
  transition: all 0.2s;
  flex: 1;
  justify-content: center;
}

.action-btn:hover {
  background-color: #f7f9fa;
  color: #1a1a1a;
}

.action-btn.liked {
  color: #e74c3c;
}

.action-btn.liked:hover {
  background-color: #fdf2f2;
}

.comments-section {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #e1e8ed;
}

.comment-input {
  display: flex;
  align-items: center;
  gap: 12px;
}

.comment-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.comment-field {
  flex: 1;
  padding: 8px 16px;
  border: 1px solid #e1e8ed;
  border-radius: 20px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.comment-field:focus {
  border-color: #4CAF50;
}

.comment-field::placeholder {
  color: #657786;
}

@media (max-width: 768px) {
  .post {
    padding: 16px;
    margin-bottom: 16px;
  }
  
  .post-actions {
    gap: 4px;
  }
  
  .action-btn {
    padding: 6px 12px;
    font-size: 13px;
  }
}
