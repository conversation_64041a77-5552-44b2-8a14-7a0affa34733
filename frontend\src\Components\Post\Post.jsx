import React, { useState } from 'react';
import './Post.css';
import { Heart, MessageCircle, Share2, MoreHorizontal } from 'lucide-react';

const Post = ({ post }) => {
  const [liked, setLiked] = useState(false);
  const [likes, setLikes] = useState(post.likes);
  const [showComments, setShowComments] = useState(false);

  const handleLike = () => {
    setLiked(!liked);
    setLikes(liked ? likes - 1 : likes + 1);
  };

  const formatTime = (timestamp) => {
    const now = new Date();
    const postTime = new Date(timestamp);
    const diffInHours = Math.floor((now - postTime) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h`;
    return `${Math.floor(diffInHours / 24)}d`;
  };

  return (
    <div className="post">
      <div className="post-header">
        <div className="post-user-info">
          <img 
            src={post.userAvatar} 
            alt={post.userName} 
            className="post-avatar"
          />
          <div className="post-user-details">
            <h4 className="post-user-name">{post.userName}</h4>
            <div className="post-meta">
              <span className="post-department">{post.department}</span>
              <span className="post-time">{formatTime(post.timestamp)}</span>
            </div>
          </div>
        </div>
        <button className="post-menu">
          <MoreHorizontal size={20} />
        </button>
      </div>

      <div className="post-content">
        <p>{post.content}</p>
        {post.image && (
          <img src={post.image} alt="Post content" className="post-image" />
        )}
        {post.hashtags && (
          <div className="post-hashtags">
            {post.hashtags.map((tag, index) => (
              <span key={index} className="hashtag">#{tag}</span>
            ))}
          </div>
        )}
      </div>

      <div className="post-stats">
        <span className="likes-count">{likes} likes</span>
        <span className="comments-count">{post.comments} comments</span>
      </div>

      <div className="post-actions">
        <button 
          className={`action-btn ${liked ? 'liked' : ''}`}
          onClick={handleLike}
        >
          <Heart size={18} fill={liked ? '#e74c3c' : 'none'} />
          Like
        </button>
        <button 
          className="action-btn"
          onClick={() => setShowComments(!showComments)}
        >
          <MessageCircle size={18} />
          Comment
        </button>
        <button className="action-btn">
          <Share2 size={18} />
          Share
        </button>
      </div>

      {showComments && (
        <div className="comments-section">
          <div className="comment-input">
            <img 
              src="https://i.pravatar.cc/32?seed=user" 
              alt="Your avatar" 
              className="comment-avatar"
            />
            <input 
              type="text" 
              placeholder="Write a comment..." 
              className="comment-field"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default Post;
