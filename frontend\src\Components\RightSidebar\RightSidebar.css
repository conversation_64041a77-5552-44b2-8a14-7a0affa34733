.right-sidebar {
  width: 300px;
  height: 100vh;
  background: #fff;
  border-left: 1px solid #e1e8ed;
  position: fixed;
  top: 60px; /* Account for navbar */
  right: 0;
  overflow-y: auto;
  padding: 20px 0;
  z-index: 998;
}

.sidebar-section {
  margin-bottom: 24px;
  padding: 0 16px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e1e8ed;
}

.section-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

.section-header svg {
  color: #4CAF50;
}

/* Trending Topics */
.trending-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.trending-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.trending-item:hover {
  background-color: #f5f7fa;
}

.trending-tag {
  font-size: 14px;
  font-weight: 500;
  color: #4CAF50;
}

.trending-count {
  font-size: 12px;
  color: #657786;
}

/* Suggested Friends */
.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 12px;
  border: 1px solid #e1e8ed;
  transition: all 0.2s;
}

.suggestion-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.suggestion-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.suggestion-info {
  flex: 1;
}

.suggestion-info h4 {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 2px 0;
}

.suggestion-info p {
  font-size: 12px;
  color: #4CAF50;
  margin: 0 0 2px 0;
}

.mutual-friends {
  font-size: 11px;
  color: #657786;
}

.connect-btn {
  background: #4CAF50;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.connect-btn:hover {
  background: #2E7D32;
}

/* Upcoming Events */
.events-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.event-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
  transition: all 0.2s;
  cursor: pointer;
}

.event-item:hover {
  background-color: #f5f7fa;
  border-color: #4CAF50;
}

.event-date {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 40px;
  background: #4CAF50;
  color: white;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
}

.event-details h4 {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 4px 0;
}

.event-details p {
  font-size: 12px;
  color: #657786;
  margin: 0;
}

/* Quick Links */
.quick-links {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.quick-link {
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  color: #1a1a1a;
  text-decoration: none;
  transition: all 0.2s;
}

.quick-link:hover {
  background-color: #f5f7fa;
  color: #4CAF50;
}

/* Responsive */
@media (max-width: 1200px) {
  .right-sidebar {
    display: none;
  }
}

/* Scrollbar styling */
.right-sidebar::-webkit-scrollbar {
  width: 6px;
}

.right-sidebar::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.right-sidebar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.right-sidebar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
