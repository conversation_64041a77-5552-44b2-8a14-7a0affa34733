import React from 'react';
import './RightSidebar.css';
import { TrendingUp, Users, Calendar, BookOpen } from 'lucide-react';

const RightSidebar = () => {
  const trendingTopics = [
    { tag: "VignanTechFest", posts: "1.2k posts" },
    { tag: "PlacementDrive", posts: "856 posts" },
    { tag: "CodingClub", posts: "642 posts" },
    { tag: "CampusLife", posts: "423 posts" },
    { tag: "StudyGroup", posts: "298 posts" }
  ];

  const suggestedFriends = [
    {
      name: "<PERSON><PERSON>",
      department: "CSE • 2nd Year",
      avatar: "https://i.pravatar.cc/40?seed=ananya",
      mutualFriends: 12
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      department: "ECE • 3rd Year", 
      avatar: "https://i.pravatar.cc/40?seed=karthik",
      mutualFriends: 8
    },
    {
      name: "<PERSON><PERSON>",
      department: "IT • 1st Year",
      avatar: "https://i.pravatar.cc/40?seed=meera",
      mutualFriends: 5
    }
  ];

  const upcomingEvents = [
    {
      title: "Tech Symposium 2024",
      date: "Dec 15",
      time: "10:00 AM",
      location: "Main Auditorium"
    },
    {
      title: "Coding Competition",
      date: "Dec 18", 
      time: "2:00 PM",
      location: "Computer Lab"
    },
    {
      title: "Cultural Fest",
      date: "Dec 22",
      time: "6:00 PM", 
      location: "Campus Ground"
    }
  ];

  return (
    <div className="right-sidebar">
      {/* Trending Topics */}
      <div className="sidebar-section">
        <div className="section-header">
          <TrendingUp size={18} />
          <h3>Trending at Vignan</h3>
        </div>
        <div className="trending-list">
          {trendingTopics.map((topic, index) => (
            <div key={index} className="trending-item">
              <span className="trending-tag">#{topic.tag}</span>
              <span className="trending-count">{topic.posts}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Suggested Friends */}
      <div className="sidebar-section">
        <div className="section-header">
          <Users size={18} />
          <h3>People You May Know</h3>
        </div>
        <div className="suggestions-list">
          {suggestedFriends.map((friend, index) => (
            <div key={index} className="suggestion-item">
              <img src={friend.avatar} alt={friend.name} className="suggestion-avatar" />
              <div className="suggestion-info">
                <h4>{friend.name}</h4>
                <p>{friend.department}</p>
                <span className="mutual-friends">{friend.mutualFriends} mutual friends</span>
              </div>
              <button className="connect-btn">Connect</button>
            </div>
          ))}
        </div>
      </div>

      {/* Upcoming Events */}
      <div className="sidebar-section">
        <div className="section-header">
          <Calendar size={18} />
          <h3>Upcoming Events</h3>
        </div>
        <div className="events-list">
          {upcomingEvents.map((event, index) => (
            <div key={index} className="event-item">
              <div className="event-date">
                <span className="date">{event.date}</span>
              </div>
              <div className="event-details">
                <h4>{event.title}</h4>
                <p>{event.time} • {event.location}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Quick Links */}
      <div className="sidebar-section">
        <div className="section-header">
          <BookOpen size={18} />
          <h3>Quick Links</h3>
        </div>
        <div className="quick-links">
          <a href="#" className="quick-link">Academic Calendar</a>
          <a href="#" className="quick-link">Library Portal</a>
          <a href="#" className="quick-link">Student Portal</a>
          <a href="#" className="quick-link">Placement Cell</a>
          <a href="#" className="quick-link">Campus Map</a>
        </div>
      </div>
    </div>
  );
};

export default RightSidebar;
