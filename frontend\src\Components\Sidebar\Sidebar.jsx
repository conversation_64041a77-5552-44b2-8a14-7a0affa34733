import React from "react";
import "./Sidebar.css";
import { Home, User, MessageCircle, Users, Rss, BookOpen, Clock } from "lucide-react";

const Sidebar = () => {
  return (
    <div className="sidebar">
      <div className="section">
        <ul className="list">
          <li>
            <Home size={18} />
            <span>Home</span>
          </li>
          <li>
            <User size={18} />
            <span>Profile</span>
          </li>
        </ul>
      </div>

      <h4 className="section-title">Favorites</h4>

      <div className="section">
        <ul className="list">
          <li>
            <MessageCircle size={18} />
            <span>Messages</span>
          </li>
          <li>
            <Users size={18} />
            <span>Friends</span>
          </li>
          <li>
            <Rss size={18} />
            <span>Feed</span>
          </li>
          <li>
            <BookOpen size={18} />
            <span>Stories</span>
          </li>
          <li>
            <Clock size={18} />
            <span>Memories</span>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default Sidebar;
