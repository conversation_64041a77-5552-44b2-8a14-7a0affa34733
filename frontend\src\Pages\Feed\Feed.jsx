import React from "react";
import "./Feed.css";

const Feed = () => {
  const posts = [
    {
      id: 1,
      title: "First Post",
      content:
        "This is a sample post inside the feed. You can display user posts, updates, or articles here.",
    },
    {
      id: 2,
      title: "Second Post",
      content:
        "Another post example — your feed will dynamically update with new content as you build further.",
    },
    {
      id: 3,
      title: "Third Post",
      content:
        "This is just placeholder text. Later, you can connect it to a backend or API for real data.",
    },
  ];

  return (
    <div className="feed">
      <div className="feed-header">
        <h2>News Feed</h2>
      </div>

      {posts.map((post) => (
        <div key={post.id} className="feed-post">
          <h3>{post.title}</h3>
          <p>{post.content}</p>
        </div>
      ))}
    </div>
  );
};

export default Feed;
