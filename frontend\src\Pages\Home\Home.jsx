import React from 'react'
import Sidebar from "../../Components/Sidebar/Sidebar.jsx"
import Navbar from '../../Components/Navbar/Navbar.jsx'
import CreatePost from '../../Components/CreatePost/CreatePost.jsx'
import Post from '../../Components/Post/Post.jsx'
import RightSidebar from '../../Components/RightSidebar/RightSidebar.jsx'
import './Home.css'

const Home = () => {
  // Sample posts data for Vignan University
  const posts = [
    {
      id: 1,
      userName: "A<PERSON><PERSON>",
      department: "CSE • 3rd Year",
      userAvatar: "https://i.pravatar.cc/48?seed=arjun",
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      content: "Just finished our Machine Learning project presentation! 🎉 Our team built an AI model to predict student performance. Thanks to Prof<PERSON> for the guidance. #MachineLearning #VignanCSE",
      image: "https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=500&h=300&fit=crop",
      likes: 24,
      comments: 8,
      hashtags: ["MachineLearning", "VignanCSE", "AI"]
    },
    {
      id: 2,
      userName: "Priya Sharma",
      department: "ECE • 2nd Year",
      userAvatar: "https://i.pravatar.cc/48?seed=priya",
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
      content: "Amazing workshop on IoT and Embedded Systems today! 🔧 Learned so much about Arduino programming and sensor integration. Can't wait to start my own project! Who else attended?",
      likes: 18,
      comments: 12,
      hashtags: ["IoT", "Arduino", "VignanECE"]
    },
    {
      id: 3,
      userName: "Vignan University",
      department: "Official",
      userAvatar: "https://i.pravatar.cc/48?seed=vignan",
      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
      content: "🎓 Congratulations to our students who secured internships at top tech companies! Microsoft, Google, Amazon, and TCS have selected 45+ students from our campus. Your hard work pays off! #PlacementSuccess #VignanPride",
      image: "https://images.unsplash.com/photo-1523240795612-9a054b0db644?w=500&h=300&fit=crop",
      likes: 156,
      comments: 23,
      hashtags: ["PlacementSuccess", "VignanPride", "Internships"]
    },
    {
      id: 4,
      userName: "Rahul Kumar",
      department: "MECH • 4th Year",
      userAvatar: "https://i.pravatar.cc/48?seed=rahul",
      timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000), // 8 hours ago
      content: "Our robotics team just won 2nd place at the National Robotics Championship! 🤖 Months of hard work finally paid off. Special thanks to our mentors and teammates. Next stop: International competition! #Robotics #VignanMech",
      likes: 67,
      comments: 15,
      hashtags: ["Robotics", "VignanMech", "Championship"]
    },
    {
      id: 5,
      userName: "Sneha Patel",
      department: "IT • 1st Year",
      userAvatar: "https://i.pravatar.cc/48?seed=sneha",
      timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours ago
      content: "First week at Vignan University has been incredible! 🌟 Made so many new friends, joined the coding club, and already working on my first web development project. College life is amazing! #FreshersLife #VignanIT #WebDev",
      likes: 31,
      comments: 19,
      hashtags: ["FreshersLife", "VignanIT", "WebDev"]
    },
    {
      id: 6,
      userName: "Dr. Rajesh Gupta",
      department: "Faculty • CSE Dept",
      userAvatar: "https://i.pravatar.cc/48?seed=drgupta",
      timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
      content: "Excited to announce our new research collaboration with IIT Hyderabad on Quantum Computing! 🔬 We'll be working on quantum algorithms and their applications. Looking for motivated students to join our research team. #QuantumComputing #Research #VignanResearch",
      likes: 89,
      comments: 27,
      hashtags: ["QuantumComputing", "Research", "VignanResearch"]
    }
  ];

  return (
    <div className="home-container">
      <Navbar/>
      <Sidebar/>
      <div className="main-content">
        <div className="feed-container">
          <CreatePost />
          {posts.map((post) => (
            <Post key={post.id} post={post} />
          ))}
        </div>
      </div>
      <RightSidebar/>
    </div>
  )
}

export default Home