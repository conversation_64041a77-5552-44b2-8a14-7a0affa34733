.login{
  display: flex;
  align-items: center;
  border: #00ff37;
  height: 100vh;
  width:100v;
  margin-left :39%;
}


.form-box {
  max-width: 30px;
  background: #f1f7fe;
  overflow: hidden;
  border-radius: 16px;
  color: #010101; 
}

.form {
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 32px 24px 24px;
  gap: 16px;
  text-align: center;
}

.title {
  font-weight: bold;
  font-size: 1.6rem;
}

.form-container {
  overflow: hidden;
  border-radius: 8px;
  background-color: #fff;
  margin: 1rem 0 .5rem;
  width: 100%;
}

.input {
  background: none;
  border: 0;
  outline: 0;
  height: 40px;
  width: 100%;
  border-bottom: 1px solid #eee;
  font-size: .9rem;
  padding: 8px 15px;
}

.form-section {
  padding: 16px;
  font-size: .85rem;
  background-color: #e0ecfb;
  box-shadow: rgb(0 0 0 / 8%) 0 -1px;
}

.form-section a {
  font-weight: bold;
  color: #00ff37;
  transition: color .3s ease;
}

.form-section a:hover {
  color: #1DB954;
  text-decoration: underline;
}

.form button {
  background-color: #1DB954;
  color: #fff;
  border: 0;
  border-radius: 24px;
  padding: 10px 16px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color .3s ease;
}

.form button:hover {
  background-color: #1DB954;
}

