

.form-box {
  max-width: 300px;
  background: #ffffff; 
  overflow: hidden;
  border-radius: 16px;
  color: #010101;
  position: relative;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1); 
}


.form {
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 32px 24px 24px;
  gap: 16px;
  text-align: center;
}

.title {
  font-weight: bold;
  font-size: 1.6rem;
}

.form-container {
  overflow: hidden;
  border-radius: 8px;
  margin: 1rem 0 0.5rem;
  width: 100%;
}

.input {
  background: none;
  border: 0;
  outline: 0;
  height: 40px;
  width: 100%;
  border-bottom: 1px solid #eee;
  font-size: 0.9rem;
  padding: 8px 15px;
}


.form-section {
  padding: 16px;
  font-size: 0.85rem;
  background: none;
  box-shadow: none;
}

.form-section a {
  font-weight: bold;
  color: #1DB954;
  transition: color 0.3s ease;
}

.form-section a:hover {
  color: #17a94d;
  text-decoration: underline;
}


.form button {
  background-color: #1DB954;
  color: #fff;
  border: 0.2px solid rgb(123, 120, 120);
  border-radius: 24px;
  padding: 10px 16px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  box-shadow: #666 1px 0.9px;
}

.form button:hover {
  background-color: #17a94d;
}
